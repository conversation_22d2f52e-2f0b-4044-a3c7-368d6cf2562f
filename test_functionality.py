#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ESP32批量刷固件工具的核心功能
"""

import sys
import os

def test_imports():
    """测试所有必要的模块导入"""
    print("测试模块导入...")
    
    try:
        import PyQt5
        print("✓ PyQt5 导入成功")
    except ImportError as e:
        print(f"✗ PyQt5 导入失败: {e}")
        return False
    
    try:
        import serial.tools.list_ports
        print("✓ pyserial 导入成功")
    except ImportError as e:
        print(f"✗ pyserial 导入失败: {e}")
        return False
    
    try:
        import esptool
        print("✓ esptool 导入成功")
    except ImportError as e:
        print(f"✗ esptool 导入失败: {e}")
        return False
    
    return True

def test_port_scanning():
    """测试端口扫描功能"""
    print("\n测试端口扫描...")
    
    try:
        import serial.tools.list_ports
        
        ports = list(serial.tools.list_ports.comports())
        print(f"✓ 发现 {len(ports)} 个串口:")
        
        for port in ports:
            print(f"  - {port.device}: {port.description}")
        
        return True
    except Exception as e:
        print(f"✗ 端口扫描失败: {e}")
        return False

def test_esptool_functionality():
    """测试esptool基本功能"""
    print("\n测试esptool功能...")
    
    try:
        import esptool
        
        # 测试esptool版本
        print(f"✓ esptool 版本: {esptool.__version__}")
        
        # 测试支持的芯片类型
        chips = ['esp32', 'esp32s2', 'esp32s3', 'esp32c3']
        print(f"✓ 支持的芯片类型: {', '.join(chips)}")
        
        return True
    except Exception as e:
        print(f"✗ esptool 测试失败: {e}")
        return False

def test_config_file():
    """测试配置文件功能"""
    print("\n测试配置文件...")
    
    try:
        import json
        
        # 创建测试配置
        test_config = {
            'baud_rate': 921600,
            'firmware_files': [
                {
                    'path': 'test.bin',
                    'address': '0x1000',
                    'name': 'test.bin'
                }
            ]
        }
        
        # 写入配置文件
        with open('test_config.json', 'w', encoding='utf-8') as f:
            json.dump(test_config, f, ensure_ascii=False, indent=2)
        
        # 读取配置文件
        with open('test_config.json', 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        # 验证配置
        assert loaded_config['baud_rate'] == 921600
        assert len(loaded_config['firmware_files']) == 1
        
        # 清理测试文件
        os.remove('test_config.json')
        
        print("✓ 配置文件读写正常")
        return True
    except Exception as e:
        print(f"✗ 配置文件测试失败: {e}")
        return False

def test_main_window_creation():
    """测试主窗口创建（无显示）"""
    print("\n测试主窗口创建...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 导入主窗口类
        from main import ESP32FlasherMainWindow
        
        # 创建主窗口（但不显示）
        window = ESP32FlasherMainWindow()
        
        # 验证窗口属性
        assert window.windowTitle() == "ESP32批量刷固件工具"
        assert hasattr(window, 'port_list')
        assert hasattr(window, 'firmware_list')
        assert hasattr(window, 'baud_rate_combo')
        
        print("✓ 主窗口创建成功")
        return True
    except Exception as e:
        print(f"✗ 主窗口创建失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("ESP32批量刷固件工具 - 功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_port_scanning,
        test_esptool_functionality,
        test_config_file,
        test_main_window_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！程序可以正常使用。")
        return True
    else:
        print("✗ 部分测试失败，请检查环境配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
