#!/bin/bash

echo "ESP32批量刷固件工具"
echo

echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.6+"
    exit 1
fi

python3 --version

echo
echo "检查依赖包..."
if ! python3 -c "import PyQt5" &> /dev/null; then
    echo "安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

echo
echo "启动程序..."
python3 main.py
