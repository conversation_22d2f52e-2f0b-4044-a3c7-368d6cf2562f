# ESP批处理工具

一个基于PyQt5和esptool.py的ESP系列芯片批量固件烧录工具，支持同时对多个ESP设备进行固件烧录。

## 功能特性

- 🔍 **自动端口扫描**: 自动发现连接的串口设备
- 🎯 **智能芯片检测**: 自动识别ESP32/ESP32-S2/ESP32-S3/ESP32-C3等芯片类型
- ✅ **端口选择**: 可以勾选需要烧录的端口，未勾选的端口不会进行烧录
- 📁 **多文件支持**: 支持选择多个固件文件，每个文件可设置不同的烧录地址
- ⚙️ **参数配置**: 可配置芯片类型、波特率和固件烧录起始地址
- 📊 **进度显示**: 实时显示每个端口的烧录进度
- 💾 **配置保存**: 自动保存界面设置参数（除端口勾选状态外）
- 📝 **详细日志**: 显示详细的烧录过程和错误信息

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install PyQt5>=5.15.0 pyserial>=3.5 esptool>=4.0.0
```

## 使用方法

1. **启动程序**
   ```bash
   python main.py
   ```

2. **扫描端口**
   - 点击"扫描端口"按钮自动发现连接的设备
   - 在端口列表中勾选需要烧录的端口

3. **添加固件文件**
   - 点击"添加固件"按钮选择固件文件（支持.bin和.hex格式）
   - 程序会使用默认起始地址，也可以在对话框中修改
   - 可以添加多个固件文件，地址会自动递增
   - 使用"编辑地址"按钮可以修改已添加文件的地址

4. **配置参数**
   - 选择芯片类型（默认auto自动检测）
   - 选择合适的波特率（默认921600）
   - 设置默认起始地址（默认0x10000）
   - 支持芯片：ESP32, ESP32-S2, ESP32-S3, ESP32-C3, ESP32-C6, ESP32-H2
   - 常用波特率：115200, 460800, 921600, 1500000
   - 常用地址：0x1000(Bootloader), 0x8000(分区表), 0x10000(应用程序)

5. **开始烧录**
   - 点击"开始烧录"按钮开始批量烧录
   - 实时查看每个端口的烧录进度
   - 在日志区域查看详细的烧录信息

6. **停止烧录**
   - 如需中断烧录，点击"停止烧录"按钮

## 界面说明

### 左侧控制面板
- **端口选择**: 显示扫描到的串口，可勾选需要烧录的端口
- **固件文件**: 管理要烧录的固件文件列表，支持添加、编辑地址、移除操作
- **烧录参数**: 配置芯片类型、波特率和默认起始地址

### 右侧显示面板
- **烧录进度**: 显示每个选中端口的烧录进度条
- **烧录日志**: 显示详细的烧录过程和状态信息

## 常用固件地址

ESP系列芯片常用的固件烧录地址：

- `0x1000` - Bootloader
- `0x8000` - Partition table
- `0xe000` - Boot app0
- `0x10000` - Application firmware
- `0x3d0000` - SPIFFS/LittleFS文件系统

## 注意事项

1. **驱动程序**: 确保已安装ESP设备的USB转串口驱动程序
2. **端口权限**: 在Linux/macOS系统上可能需要串口访问权限
3. **固件兼容性**: 确保固件文件与目标ESP芯片兼容
4. **芯片类型**: 建议使用自动检测，或手动选择正确的芯片类型
5. **烧录地址**: 请根据实际固件要求设置正确的烧录地址
6. **设备连接**: 烧录前确保ESP设备处于下载模式

## 故障排除

### 端口扫描不到设备
- 检查USB连接
- 确认驱动程序已正确安装
- 尝试重新插拔设备

### 烧录失败
- 检查波特率设置是否合适
- 确认固件文件路径正确
- 检查烧录地址是否正确
- 确保设备处于下载模式

### 权限错误（Linux/macOS）
```bash
sudo usermod -a -G dialout $USER
# 然后重新登录
```

## 配置文件

程序会自动在当前目录生成`config.json`配置文件，保存以下设置：
- 芯片类型配置
- 波特率配置
- 默认起始地址设置
- 固件文件列表及对应地址

注意：端口勾选状态不会保存，每次启动需要重新选择。

## 技术实现

- **界面框架**: PyQt5
- **串口通信**: pyserial
- **固件烧录**: esptool.py
- **多线程**: 每个端口使用独立线程进行烧录
- **进度反馈**: 实时更新烧录进度和状态

## 许可证

本项目采用MIT许可证。
