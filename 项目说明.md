# ESP批处理工具 - 项目说明

## 项目结构

```
esp32piliangshaolu/
├── main.py                 # 主程序文件
├── requirements.txt        # Python依赖包列表
├── README.md              # 项目说明文档
├── 项目说明.md            # 中文项目说明
├── run.bat                # Windows启动脚本
├── run.sh                 # Linux/macOS启动脚本
├── test_functionality.py  # 功能测试脚本
├── example_config.json    # 示例配置文件
└── config.json            # 自动生成的配置文件（运行后生成）
```

## 核心功能实现

### 1. 端口扫描 (PortScanner类)
- 使用`serial.tools.list_ports`扫描可用串口
- 在独立线程中运行，避免界面卡顿
- 返回端口设备名、描述和硬件ID

### 2. 固件烧录 (FlashWorker类)
- 使用`esptool`库进行实际的固件烧录
- 支持多文件烧录，每个文件可设置不同地址
- 实时进度反馈和详细日志输出
- 支持取消操作

### 3. 用户界面 (ESP32FlasherMainWindow类)
- 基于PyQt5的现代化界面
- 左右分栏布局：控制面板 + 进度日志
- 实时进度条显示每个端口的烧录状态
- 详细的操作日志

### 4. 配置管理
- 自动保存/加载用户设置
- JSON格式配置文件
- 保存波特率、固件文件列表等参数

## 技术特点

### 多线程设计
- 端口扫描线程：避免界面冻结
- 烧录工作线程：每个端口独立线程，支持并行烧录
- 主界面线程：响应用户操作和更新显示

### 错误处理
- 完善的异常捕获和错误提示
- 固件文件存在性检查
- 端口连接状态验证

### 用户体验
- 直观的进度显示
- 详细的操作日志
- 参数自动保存
- 一键启动脚本

## 使用场景

### 1. 生产环境批量烧录
- 同时连接多个ESP32设备
- 批量烧录相同固件
- 提高生产效率

### 2. 开发测试
- 快速烧录测试固件
- 支持多种固件文件组合
- 灵活的地址配置

### 3. 固件更新
- 批量更新设备固件
- 保持配置一致性
- 减少人工操作错误

## 扩展功能建议

### 1. 固件验证
- 烧录后自动验证
- MD5/SHA256校验
- 固件版本检查

### 2. 设备管理
- 设备序列号记录
- 烧录历史追踪
- 设备状态监控

### 3. 批处理脚本
- 命令行模式支持
- 自动化脚本集成
- CI/CD流水线支持

### 4. 高级配置
- 自定义芯片类型
- 高级esptool参数
- 烧录策略配置

## 常见问题解决

### 1. 端口权限问题
```bash
# Linux/macOS
sudo usermod -a -G dialout $USER
# 重新登录生效
```

### 2. 驱动程序问题
- 安装CP210x或CH340驱动
- 检查设备管理器中的端口状态

### 3. 烧录失败
- 检查波特率设置
- 确认设备处于下载模式
- 验证固件文件完整性

### 4. 依赖包问题
```bash
# 重新安装依赖
pip uninstall PyQt5 pyserial esptool
pip install -r requirements.txt
```

## 开发环境要求

- Python 3.6+
- PyQt5 5.15+
- pyserial 3.5+
- esptool 4.0+

## 许可证

本项目采用MIT许可证，允许自由使用、修改和分发。
